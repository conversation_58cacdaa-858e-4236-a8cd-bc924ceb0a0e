// API client for connecting to the backend
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4001/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // Only run on client side
      if (typeof window !== 'undefined') {
        // Clear local storage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        // Redirect to login page if not already there
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  login: async (credentials: { email: string; password: string }) => {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  },
  
  register: async (userData: any) => {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  },
};

// Families API
export const familiesApi = {
  getAll: async () => {
    const response = await apiClient.get('/families');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await apiClient.get(`/families/${id}`);
    return response.data;
  },
  
  create: async (formData: FormData) => {
    // Special case for file uploads - need to set different content type
    const response = await apiClient.post('/families', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/families/${id}`);
    return response.data;
  },
  
  update: async (id: string, formData: FormData) => {
    const response = await apiClient.put(`/families/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// Offerings API
export const offeringsApi = {
  getAll: async (params = {}) => {
    const response = await apiClient.get('/offerings', { params });
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await apiClient.get(`/offerings/${id}`);
    return response.data;
  },
  
  create: async (data: any) => {
    const response = await apiClient.post('/offerings', data);
    return response.data;
  },
  
  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/offerings/${id}`, data);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/offerings/${id}`);
    return response.data;
  }
};

// Events API
export const eventsApi = {
  getAll: async () => {
    const response = await apiClient.get('/events');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await apiClient.get(`/events/${id}`);
    return response.data;
  },
  
  create: async (eventData: any) => {
    const response = await apiClient.post('/events', eventData);
    return response.data;
  },
  
  update: async (id: string, eventData: any) => {
    const response = await apiClient.put(`/events/${id}`, eventData);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/events/${id}`);
    return response.data;
  },
};

// Users API
export const usersApi = {
  getAll: async () => {
    const response = await apiClient.get('/users');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await apiClient.get(`/users/${id}`);
    return response.data;
  },
  
  create: async (userData: any) => {
    const response = await apiClient.post('/users', userData);
    return response.data;
  },
  
  update: async (id: string, userData: any) => {
    const response = await apiClient.put(`/users/${id}`, userData);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/users/${id}`);
    return response.data;
  },
  
  resetPassword: async (id: string, newPassword: string) => {
    const response = await apiClient.post(`/users/${id}/reset-password`, { newPassword });
    return response.data;
  },
};

// Settings API
export const settingsApi = {
  get: async () => {
    const response = await apiClient.get('/settings');
    return response.data;
  },
  
  update: async (settingsData: any) => {
    const response = await apiClient.put('/settings', settingsData);
    return response.data;
  },
};

// Offering Types API
export const offeringTypesApi = {
  getAll: async () => {
    const response = await apiClient.get('/offering-types');
    return response.data;
  },
  
  create: async (data: { name: string }) => {
    const response = await apiClient.post('/offering-types', data);
    return response.data;
  },
  
  update: async (id: string, data: { name: string, isActive: boolean }) => {
    const response = await apiClient.put(`/offering-types/${id}`, data);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/offering-types/${id}`);
    return response.data;
  }
};

// Payment Types API
export const paymentTypesApi = {
  getAll: async () => {
    const response = await apiClient.get('/payment-types');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await apiClient.get(`/payment-types/${id}`);
    return response.data;
  },
  
  create: async (data: {
    name: string,
    description: string,
    amount: number,
    frequency: string,
    startYear?: number,
    endYear?: number,
    startMonth?: number,
    endMonth?: number
  }) => {
    const response = await apiClient.post('/payment-types', data);
    return response.data;
  },
  
  update: async (id: string, data: {
    name: string,
    description: string,
    amount: number,
    frequency: string,
    isActive: boolean,
    startYear?: number,
    endYear?: number,
    startMonth?: number,
    endMonth?: number
  }) => {
    const response = await apiClient.put(`/payment-types/${id}`, data);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/payment-types/${id}`);
    return response.data;
  }
};

// Payments API
export const paymentsApi = {
  getAll: async (params = {}) => {
    const response = await apiClient.get('/payments', { params });
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await apiClient.get(`/payments/${id}`);
    return response.data;
  },
  
  create: async (data: any) => {
    // If this is a direct payment (not for a due), we need to create it
    try {
      const response = await apiClient.post('/payments', data);
      return response.data;
    } catch (error: any) {
      // If the direct endpoint fails, try the record payment endpoint
      if (error.response && error.response.status === 404) {
        console.warn('Direct payment endpoint not found, trying record payment endpoint');
        return paymentsApi.recordPayment({
          ...data,
          isPartialPayment: data.isPartialPayment || false
        });
      }
      throw error;
    }
  },
  
  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/payments/${id}`, data);
    return response.data;
  },
  
  delete: async (id: string) => {
    const response = await apiClient.delete(`/payments/${id}`);
    return response.data;
  },
  
  // Payment Dues API
  getAllDues: async (params = {}) => {
    const response = await apiClient.get('/payments/dues', { params });
    return response.data;
  },
  
  getDueById: async (id: string) => {
    const response = await apiClient.get(`/payments/dues/${id}`);
    return response.data;
  },
  
  recordPayment: async (data: {
    dueId: string,
    amount: number,
    paymentDate: string,
    receiptNumber: string,
    paymentMethod: string,
    notes?: string,
    isPartialPayment: boolean
  }) => {
    const response = await apiClient.post('/payments/record', data);
    return response.data;
  },
  
  generateDues: async (data: {
    paymentTypeId: string,
    year: number,
    dueDate: string,
    familyFilter?: string,
    wardFilter?: string
  }) => {
    const response = await apiClient.post('/payments/generate-dues', data);
    return response.data;
  },
  
  previewDues: async (data: {
    paymentTypeId: string,
    year: number,
    dueDate: string,
    familyFilter?: string,
    wardFilter?: string
  }) => {
    const response = await apiClient.post('/payments/preview-dues', data);
    return response.data;
  },
  
  searchDues: async (query: string) => {
    const response = await apiClient.get('/payments/search-dues', { 
      params: { query } 
    });
    return response.data;
  },
  
  getPendingByFamily: async (familyId: string) => {
    const response = await apiClient.get(`/payments/pending/family/${familyId}`);
    return response.data;
  },

  getRelatedDues: async (familyId: string, paymentTypeId: string, params?: {
    startYear?: number,
    endYear?: number,
    startMonth?: number,
    endMonth?: number
  }) => {
    const response = await apiClient.get(`/payments/related-dues/${familyId}/${paymentTypeId}`, { params });
    return response.data;
  },
  
  getSummaryByYear: async (year: number) => {
    const response = await apiClient.get(`/payments/summary/year/${year}`);
    return response.data;
  }
};
export default apiClient;
